<template>
  <div class="p-4">
    <SmartDataTable 
      title="Post Management"
      :store="postStore"
      fetch-method="fetchPosts"
      delete-method="deletePost"
      :column-config="columnConfig"
      :default-actions="['view', 'edit', 'delete']"
      @edit="handleEdit"
      @view="handleView"
    />
  </div>
</template>

<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
// import usePostStore from '@/stores/post' // Hypothetical store

// For demo purposes, using user store
import useUserStore from '@/stores/user'
const postStore = useUserStore() // Replace with actual post store

// Optional: Custom column configurations
const columnConfig = {
  id: { width: '80px' },
  title: { title: 'Post Title', width: '300px' },
  status: { 
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'published', label: 'Published' },
      { value: 'draft', label: 'Draft' },
      { value: 'archived', label: 'Archived' }
    ]
  },
  author: { title: 'Author' },
  category: { 
    title: 'Category',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Categories' },
      { value: 'tech', label: 'Technology' },
      { value: 'business', label: 'Business' },
      { value: 'lifestyle', label: 'Lifestyle' }
    ]
  }
}

// Handle custom actions
function handleEdit(post) {
  console.log('Edit post:', post)
  // Example: router.push(`/admin/posts/edit/${post.id}`)
}

function handleView(post) {
  console.log('View post:', post)
  // Example: router.push(`/admin/posts/view/${post.id}`)
}
</script>

<!-- 
This example shows how easy it is to create a new data table:
1. Just 50 lines of code (vs 280+ lines before)
2. Auto-generates columns from data structure
3. Handles all server-side operations automatically
4. Only need to specify custom configurations when needed
5. CRUD operations work out of the box

To use this for real posts:
1. Create a post store with fetchPosts() and deletePost() methods
2. Replace the import and store reference
3. Customize columnConfig if needed
4. Add edit/view handlers
5. Done!
-->
