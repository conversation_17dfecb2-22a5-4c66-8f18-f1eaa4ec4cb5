import { ref } from 'vue'

import { defineStore } from 'pinia'

export const useToastStore = defineStore('toast', () => {
  const toasts = ref([])
  let toastId = 0

  /**
   * Add a new toast notification
   * @param {Object} options - Toast configuration
   * @param {string} options.message - Toast message (required)
   * @param {string} options.title - Toast title (optional)
   * @param {string} options.type - Toast type: 'success', 'error', 'warning', 'info', 'default'
   * @param {number} options.duration - Auto-dismiss duration in ms (0 = no auto-dismiss)
   * @param {boolean} options.closable - Whether toast can be manually closed
   * @param {Object} options.data - Additional data to store with toast
   */
  function addToast(options) {
    const toast = {
      id: ++toastId,
      message: options.message || '',
      title: options.title || null,
      type: options.type || 'default',
      duration: options.duration !== undefined ? options.duration : 5000,
      closable: options.closable !== false,
      data: options.data || null,
      createdAt: Date.now()
    }

    toasts.value.push(toast)

    // Auto-remove toast after duration
    if (toast.duration > 0) {
      setTimeout(() => {
        removeToast(toast.id)
      }, toast.duration)
    }

    return toast.id
  }

  /**
   * Remove a toast by ID
   * @param {number} id - Toast ID
   */
  function removeToast(id) {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  /**
   * Clear all toasts
   */
  function clearAllToasts() {
    toasts.value = []
  }

  /**
   * Convenience methods for different toast types
   */
  function success(message, options = {}) {
    return addToast({
      message,
      type: 'success',
      ...options
    })
  }

  function error(message, options = {}) {
    return addToast({
      message,
      type: 'error',
      duration: 0, // Errors don't auto-dismiss by default
      ...options
    })
  }

  function warning(message, options = {}) {
    return addToast({
      message,
      type: 'warning',
      ...options
    })
  }

  function info(message, options = {}) {
    return addToast({
      message,
      type: 'info',
      ...options
    })
  }

  /**
   * Advanced toast methods
   */
  function loading(message, options = {}) {
    return addToast({
      message,
      type: 'info',
      duration: 0, // Loading toasts don't auto-dismiss
      closable: false, // Can't be manually closed
      ...options
    })
  }

  function updateToast(id, updates) {
    const toast = toasts.value.find(t => t.id === id)
    if (toast) {
      Object.assign(toast, updates)
    }
  }

  // Promise-based toast for async operations
  function promise(promise, messages = {}) {
    const loadingId = loading(messages.loading || 'Loading...')

    return promise
      .then(result => {
        removeToast(loadingId)
        success(messages.success || 'Operation completed successfully!')
        return result
      })
      .catch(error => {
        removeToast(loadingId)
        const errorMessage = error?.message || messages.error || 'Operation failed!'
        error(errorMessage)
        throw error
      })
  }

  return {
    // State
    toasts,

    // Actions
    addToast,
    removeToast,
    clearAllToasts,
    updateToast,

    // Convenience methods
    success,
    error,
    warning,
    info,
    loading,
    promise
  }
})
