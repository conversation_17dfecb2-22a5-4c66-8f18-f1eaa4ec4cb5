<template>
  <div class="p-8 space-y-6">
    <h1 class="text-3xl font-bold text-gray-900">Toast System Examples</h1>
    
    <!-- Basic Toast Examples -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Basic Toast Types</h2>
      <div class="space-x-4">
        <button @click="showSuccess" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
          Success Toast
        </button>
        <button @click="showError" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
          Error Toast
        </button>
        <button @click="showWarning" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
          Warning Toast
        </button>
        <button @click="showInfo" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Info Toast
        </button>
      </div>
    </div>

    <!-- Advanced Toast Examples -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Advanced Examples</h2>
      <div class="space-x-4">
        <button @click="showWithTitle" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
          Toast with Title
        </button>
        <button @click="showPersistent" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
          Persistent Toast
        </button>
        <button @click="showLoading" class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
          Loading Toast
        </button>
        <button @click="simulateApiCall" class="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600">
          API Call Example
        </button>
      </div>
    </div>

    <!-- User Operations Examples -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">User Operations Examples</h2>
      <div class="space-x-4">
        <button @click="simulateUserCreate" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
          Create User
        </button>
        <button @click="simulateUserUpdate" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Update User
        </button>
        <button @click="simulateUserDelete" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
          Delete User
        </button>
        <button @click="simulateBulkDelete" class="px-4 py-2 bg-red-700 text-white rounded hover:bg-red-800">
          Bulk Delete
        </button>
      </div>
    </div>

    <!-- Management -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Toast Management</h2>
      <div class="space-x-4">
        <button @click="clearAll" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
          Clear All Toasts
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useToast } from '@/composables/useToast'

const toast = useToast()

// Basic examples
function showSuccess() {
  toast.success('Operation completed successfully!')
}

function showError() {
  toast.error('Something went wrong!')
}

function showWarning() {
  toast.warning('Please check your input')
}

function showInfo() {
  toast.info('Here is some information')
}

// Advanced examples
function showWithTitle() {
  toast.success('User profile updated successfully!', {
    title: 'Profile Updated',
    duration: 4000
  })
}

function showPersistent() {
  toast.error('This error requires your attention', {
    title: 'Critical Error',
    duration: 0 // Won't auto-dismiss
  })
}

function showLoading() {
  const loadingId = toast.loading('Processing your request...')
  
  // Simulate work
  setTimeout(() => {
    toast.remove(loadingId)
    toast.success('Request processed successfully!')
  }, 3000)
}

function simulateApiCall() {
  // Using the API helper method
  const apiCall = new Promise((resolve, reject) => {
    setTimeout(() => {
      Math.random() > 0.5 ? resolve('Success!') : reject(new Error('API Error'))
    }, 2000)
  })

  toast.api(apiCall, {
    loadingMessage: 'Calling API...',
    successMessage: 'API call successful!',
    errorMessage: 'API call failed!'
  })
}

// User operation examples
function simulateUserCreate() {
  toast.promise(
    new Promise((resolve) => setTimeout(resolve, 1500)),
    {
      loading: 'Creating user...',
      success: 'User created successfully!',
      error: 'Failed to create user'
    }
  )
}

function simulateUserUpdate() {
  const loadingId = toast.loading('Updating user profile...')
  
  setTimeout(() => {
    toast.remove(loadingId)
    toast.success('User profile updated successfully!', {
      title: 'Update Complete'
    })
  }, 2000)
}

function simulateUserDelete() {
  toast.warning('User will be deleted permanently', {
    title: 'Confirm Deletion',
    duration: 0
  })
  
  setTimeout(() => {
    toast.success('User deleted successfully')
  }, 1000)
}

function simulateBulkDelete() {
  const count = 5
  toast.promise(
    new Promise((resolve) => setTimeout(resolve, 2000)),
    {
      loading: `Deleting ${count} users...`,
      success: `Successfully deleted ${count} users`,
      error: 'Failed to delete users'
    }
  )
}

function clearAll() {
  toast.clear()
}
</script>
