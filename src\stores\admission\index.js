import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'

const useAdmissionStore = defineStore("useAdmissionStore", {
    state: () => ({
        admissions: [],      // ← Main data array
        totalPages: 1,       // ← Required for pagination
        totalRecords: 0,     // ← Required for pagination info
        loading: false,
    }),

    actions: {
        // Required method with exact signature for SmartDataTable
        async fetchAdmissions(params = {}) {
            this.loading = true;
            try {
                // Extract parameters with defaults and ensure proper types
                const {
                    page = 1,
                    per_page = 10,
                    search = '',
                    sort_field = null,
                    sort_direction = null,
                    ...filters
                } = params;

                // Convert to proper types to avoid Laravel errors
                const pageNum = parseInt(page) || 1;
                const limitNum = parseInt(per_page) || 10;

                // Build query parameters
                let url = `/admissions?page=${pageNum}&limit=${limitNum}`;

                // Add search if provided
                if (search && search.trim()) {
                    url += `&search=${encodeURIComponent(search.trim())}`;
                }

                // Add sorting if provided
                if (sort_field && sort_direction) {
                    url += `&sort=${sort_field}&direction=${sort_direction}`;
                    console.log(`Adding sort parameters: sort=${sort_field}, direction=${sort_direction}`);
                }

                // Add filters if provided
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        url += `&filter[${key}]=${encodeURIComponent(value)}`;
                    }
                });
                const response = await api.get(url);

                // Update store state
                this.user = response.data.data.data || [];
                this.totalPages = response.data.data.last_page || 1;
                this.totalRecords = response.data.data.total || 0;

                // Return data in the format expected by UserList.vue
                return {
                    data: this.user,
                    current_page: response.data.data.current_page || pageNum,
                    per_page: response.data.data.per_page || limitNum,
                    total: response.data.data.total || 0,
                    last_page: response.data.data.last_page || 1
                };
            } catch (err) {
                console.error("Error fetching users:", err);
                toast.error("Failed to fetch users");
                this.user = [];

                // Return empty data structure on error
                return {
                    data: [],
                    current_page: 1,
                    per_page: 10,
                    total: 0,
                    last_page: 1
                };
            } finally {
                this.loading = false;
            }
        },

        // Required delete method for SmartDataTable
        async deleteAdmission(id) {
            this.loading = true;
            try {
                const response = await api.delete(`/admissions/${id}`);
                if (response.data) {
                    // Remove from local state
                    this.admissions = this.admissions.filter((admission) => admission.id !== id);
                    toast.success("Admission deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting admission:", err);
                toast.error("Failed to delete admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        // Optional: Additional methods for CRUD operations
        async createAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.post("/admissions", admissionData);
                if (response.data) {
                    this.admissions.push(response.data.data.admission);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating admission:", err);
                toast.error(err.response?.data?.message || "Failed to create admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.put(`/admissions/${admissionData.id}`, admissionData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the admission list to get updated data
                    await this.fetchAdmissions();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating admission:", err);
                toast.error(err.response?.data?.message || "Failed to update admission");
                return false;
            } finally {
                this.loading = false;
            }
        }
    },
});

export default useAdmissionStore;
