<template>
  <div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold text-gray-900">Status Filter Debug</h1>
    
    <!-- Debug Information -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h2 class="text-lg font-semibold text-yellow-800 mb-2">Debug Information</h2>
      <div class="space-y-2 text-sm">
        <div><strong>Sample Data Status Values:</strong> {{ sampleStatusValues }}</div>
        <div><strong>Filter Options:</strong> {{ filterOptions.map(o => o.value) }}</div>
        <div><strong>Current Filter Value:</strong> {{ currentFilter }}</div>
      </div>
    </div>

    <!-- Test Different Status Configurations -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      
      <!-- Configuration 1: String Values -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4">String Status Values</h3>
        <SmartDataTable 
          title="Users - String Status"
          :store="userStore"
          fetch-method="fetchUser"
          delete-method="deleteUser"
          :column-config="stringStatusConfig"
          @add="handleAdd"
        />
      </div>

      <!-- Configuration 2: Numeric Values -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4">Numeric Status Values</h3>
        <SmartDataTable 
          title="Users - Numeric Status"
          :store="userStore"
          fetch-method="fetchUser"
          delete-method="deleteUser"
          :column-config="numericStatusConfig"
          @add="handleAdd"
        />
      </div>

      <!-- Configuration 3: Mixed Values -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4">Mixed Status Values</h3>
        <SmartDataTable 
          title="Users - Mixed Status"
          :store="userStore"
          fetch-method="fetchUser"
          delete-method="deleteUser"
          :column-config="mixedStatusConfig"
          @add="handleAdd"
        />
      </div>

      <!-- Configuration 4: Auto-Detect -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4">Auto-Detect Status Values</h3>
        <SmartDataTable 
          title="Users - Auto Status"
          :store="userStore"
          fetch-method="fetchUser"
          delete-method="deleteUser"
          :column-config="autoStatusConfig"
          @add="handleAdd"
        />
      </div>
    </div>

    <!-- Configuration Examples -->
    <div class="bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Configuration Examples</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="font-medium mb-2">String Status Config:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(stringStatusConfig.status, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Numeric Status Config:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(numericStatusConfig.status, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Mixed Status Config:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(mixedStatusConfig.status, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Auto Status Config:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(autoStatusConfig.status, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- Manual Test -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Manual Filter Test</h2>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Test Status Filter:</label>
          <SearchableSelect 
            v-model="currentFilter"
            :options="filterOptions"
            placeholder="Select status..."
            @update:model-value="handleFilterChange"
          />
        </div>
        <div class="text-sm text-gray-600">
          Selected Value: <code class="bg-gray-100 px-2 py-1 rounded">{{ currentFilter }}</code>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import SearchableSelect from '@/components/DataTable/SearchableSelect.vue'
import { toast } from '@/plugins/toast'

const userStore = useUserStore()
const currentFilter = ref('')

// Base columns for all examples
const baseColumns = {
  id: { title: 'ID', width: '80px', filterable: true },
  name: { title: 'Full Name', filterable: true },
  email: { title: 'Email', filterable: true }
}

// Different status configurations
const stringStatusConfig = {
  ...baseColumns,
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ],
    badgeConfig: {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-red-100 text-red-800'
    }
  }
}

const numericStatusConfig = {
  ...baseColumns,
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 1, label: 'Active' },
      { value: 0, label: 'Inactive' }
    ],
    badgeConfig: {
      1: 'bg-green-100 text-green-800',
      0: 'bg-red-100 text-red-800'
    }
  }
}

const mixedStatusConfig = {
  ...baseColumns,
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'active', label: 'Active (String)' },
      { value: 'inactive', label: 'Inactive (String)' },
      { value: 1, label: 'Active (Number)' },
      { value: 0, label: 'Inactive (Number)' },
      { value: '1', label: 'Active (String 1)' },
      { value: '0', label: 'Inactive (String 0)' }
    ],
    badgeConfig: {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-red-100 text-red-800',
      1: 'bg-green-100 text-green-800',
      0: 'bg-red-100 text-red-800',
      '1': 'bg-green-100 text-green-800',
      '0': 'bg-red-100 text-red-800'
    }
  }
}

const autoStatusConfig = {
  ...baseColumns,
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    // No filterOptions - will be auto-generated
    badgeConfig: {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-red-100 text-red-800',
      1: 'bg-green-100 text-green-800',
      0: 'bg-red-100 text-red-800'
    }
  }
}

// Debug computed properties
const sampleStatusValues = computed(() => {
  if (userStore.data && userStore.data.length > 0) {
    return [...new Set(userStore.data.map(user => user.status))].slice(0, 5)
  }
  return ['No data loaded']
})

const filterOptions = computed(() => mixedStatusConfig.status.filterOptions)

// Methods
function handleAdd() {
  toast.info('Add button clicked!')
}

function handleFilterChange(value) {
  console.log('Filter changed to:', value, typeof value)
  toast.info(`Status filter changed to: ${value}`)
}

// Load data on mount
onMounted(() => {
  if (userStore.data.length === 0) {
    userStore.fetchUser()
  }
})
</script>
