<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">DataTable Test</h1>
    <DataTable :data="testData" :columns="testColumns" :config="testConfig" />
  </div>
</template>npx tailwindcss init -p

<script setup>
import { ref } from 'vue'

import DataTable from '@/components/DataTable/DataTable.vue'

const testData = ref([
  { id: 1, name: 'Product 1', price: 100 },
  { id: 2, name: 'Product 2', price: 200 },
  { id: 3, name: 'Product 3', price: 300 }
])

const testColumns = ref([
  { key: 'id', title: 'ID', sortable: true },
  { key: 'name', title: 'Name', sortable: true },
  { key: 'price', title: 'Price', type: 'currency', sortable: true }
])

const testConfig = ref({
  pagination: { enabled: true, pageSize: 10 },
  filtering: { enabled: true },
  ui: { striped: true, hover: true }
})
</script>

<style></style>