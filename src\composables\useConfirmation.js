import { ref, reactive } from 'vue'

// Global state for confirmation modal
const confirmationState = reactive({
  show: false,
  title: '',
  message: '',
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  type: 'danger',
  loading: false,
  resolve: null,
  reject: null
})

export function useConfirmation() {
  
  function showConfirmation(options = {}) {
    return new Promise((resolve, reject) => {
      // Set up the confirmation modal
      confirmationState.show = true
      confirmationState.title = options.title || 'Confirm Action'
      confirmationState.message = options.message || 'Are you sure you want to proceed?'
      confirmationState.confirmText = options.confirmText || 'Confirm'
      confirmationState.cancelText = options.cancelText || 'Cancel'
      confirmationState.type = options.type || 'danger'
      confirmationState.loading = false
      confirmationState.resolve = resolve
      confirmationState.reject = reject
    })
  }

  function showDeleteConfirmation(options = {}) {
    return showConfirmation({
      title: 'Delete Record',
      message: 'Are you sure you want to delete this record? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger',
      ...options
    })
  }

  function showBulkDeleteConfirmation(count, options = {}) {
    return showConfirmation({
      title: 'Delete Multiple Records',
      message: `Are you sure you want to delete ${count} record${count > 1 ? 's' : ''}? This action cannot be undone.`,
      confirmText: `Delete ${count} Record${count > 1 ? 's' : ''}`,
      cancelText: 'Cancel',
      type: 'danger',
      ...options
    })
  }

  function handleConfirm() {
    if (confirmationState.resolve) {
      confirmationState.loading = true
      // Small delay to show loading state
      setTimeout(() => {
        confirmationState.resolve(true)
        closeConfirmation()
      }, 300)
    }
  }

  function handleCancel() {
    if (confirmationState.resolve) {
      confirmationState.resolve(false)
    }
    closeConfirmation()
  }

  function closeConfirmation() {
    confirmationState.show = false
    confirmationState.loading = false
    confirmationState.resolve = null
    confirmationState.reject = null
  }

  return {
    // State
    confirmationState,
    
    // Methods
    showConfirmation,
    showDeleteConfirmation,
    showBulkDeleteConfirmation,
    handleConfirm,
    handleCancel,
    closeConfirmation
  }
}

// Export the global state for use in components
export { confirmationState }
