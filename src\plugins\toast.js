import { useToast } from '@/composables/useToast'

/**
 * Toast Plugin for Vue 3
 * Provides global access to toast notifications
 */
export default {
  install(app) {
    // Create toast instance
    const toast = useToast()

    // Add to global properties for Options API
    app.config.globalProperties.$toast = toast

    // Provide for Composition API
    app.provide('toast', toast)

    // Add global methods
    app.config.globalProperties.$success = toast.success
    app.config.globalProperties.$error = toast.error
    app.config.globalProperties.$warning = toast.warning
    app.config.globalProperties.$info = toast.info
    app.config.globalProperties.$loading = toast.loading
  }
}

/**
 * Global toast instance for use outside of Vue components
 */
let globalToast = null

export function initGlobalToast() {
  if (!globalToast) {
    globalToast = useToast()
  }
  return globalToast
}

export function getGlobalToast() {
  return globalToast || initGlobalToast()
}

// Export convenience methods for global use
export const toast = {
  success: (message, options) => getGlobalToast().success(message, options),
  error: (message, options) => getGlobalToast().error(message, options),
  warning: (message, options) => getGlobalToast().warning(message, options),
  info: (message, options) => getGlobalToast().info(message, options),
  loading: (message, options) => getGlobalToast().loading(message, options),
  remove: (id) => getGlobalToast().remove(id),
  clear: () => getGlobalToast().clear(),
  api: (apiCall, options) => getGlobalToast().api(apiCall, options),
  promise: (promise, messages) => getGlobalToast().promise(promise, messages)
}
