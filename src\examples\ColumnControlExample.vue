<template>
  <div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold text-gray-900">Column Control Examples</h1>
    
    <!-- Example 1: Only Specified Columns -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">1. Show Only Specified Columns</h2>
      <p class="text-gray-600 mb-4">Only ID, Name, Email, and Status columns will be shown</p>
      
      <SmartDataTable 
        title="Users - Specific Columns Only"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :column-config="specificColumnsConfig"
        @add="handleAdd"
      />
    </div>

    <!-- Example 2: Control Filters Per Column -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">2. Control Filters Per Column</h2>
      <p class="text-gray-600 mb-4">Image has no filter, others have filters</p>
      
      <SmartDataTable 
        title="Users - Selective Filters"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :column-config="selectiveFiltersConfig"
        @add="handleAdd"
      />
    </div>

    <!-- Example 3: Different Filter Types -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">3. Different Filter Types</h2>
      <p class="text-gray-600 mb-4">Text filters, select filters, and disabled filters</p>
      
      <SmartDataTable 
        title="Users - Mixed Filter Types"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :column-config="mixedFiltersConfig"
        @add="handleAdd"
      />
    </div>

    <!-- Configuration Examples -->
    <div class="bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Configuration Examples</h2>
      
      <div class="space-y-6">
        <div>
          <h3 class="font-medium mb-2">1. Specific Columns Only:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(specificColumnsConfig, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">2. Selective Filters:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(selectiveFiltersConfig, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">3. Mixed Filter Types:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(mixedFiltersConfig, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import { toast } from '@/plugins/toast'

const userStore = useUserStore()

// Example 1: Only show specific columns (no auto-generation)
const specificColumnsConfig = {
  id: { 
    title: 'ID',
    width: '80px',
    filterable: true
  },
  name: { 
    title: 'Full Name',
    filterable: true
  },
  email: { 
    title: 'Email',
    filterable: true
  },
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 1, label: 'Active' },
      { value: 0, label: 'Inactive' }
    ]
  }
  // ONLY these 4 columns will show - no image, position, dates, etc.
}

// Example 2: Control which columns have filters
const selectiveFiltersConfig = {
  id: { 
    title: 'ID',
    width: '80px',
    filterable: false  // No filter for ID
  },
  name: { 
    title: 'Full Name',
    filterable: true   // Filter enabled
  },
  email: { 
    title: 'Email',
    filterable: true   // Filter enabled
  },
  image_url_full: {
    title: 'Profile Image',
    type: 'image',
    width: '100px',
    filterable: false  // No filter for image
  },
  user_position: { 
    title: 'Position',
    filterable: true   // Filter enabled
  },
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,  // Filter enabled
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 1, label: 'Active' },
      { value: 0, label: 'Inactive' }
    ]
  }
}

// Example 3: Different filter types
const mixedFiltersConfig = {
  id: { 
    title: 'ID',
    width: '80px',
    filterable: true,
    filterType: 'number'  // Number filter
  },
  name: { 
    title: 'Full Name',
    filterable: true,
    filterType: 'text'    // Text filter (default)
  },
  email: { 
    title: 'Email',
    filterable: true,
    filterType: 'text'    // Text filter
  },
  user_position: { 
    title: 'Position',
    filterable: true,
    filterType: 'select', // Select filter
    filterOptions: [
      { value: '', label: 'All Positions' },
      { value: 'manager', label: 'Manager' },
      { value: 'developer', label: 'Developer' },
      { value: 'designer', label: 'Designer' }
    ]
  },
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select', // Select filter
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 1, label: 'Active' },
      { value: 0, label: 'Inactive' }
    ]
  },
  created_at: {
    title: 'Created Date',
    type: 'date',
    filterable: false     // No filter for dates
  }
}

function handleAdd() {
  toast.info('Add button clicked!')
}
</script>
