<template>
  <div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold text-gray-900">Add Button Examples</h1>
    
    <!-- Example 1: Basic Add Button -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">1. Basic Add Button</h2>
      <p class="text-gray-600 mb-4">Simple Add button with default styling</p>
      
      <SmartDataTable 
        title="Users with Basic Add Button"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :table-config="basicConfig"
        @add="handleBasicAdd"
      />
    </div>

    <!-- Example 2: Custom Styled Add Button -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">2. Custom Styled Add Button</h2>
      <p class="text-gray-600 mb-4">Add button with custom text, color, and size</p>
      
      <SmartDataTable 
        title="Users with Custom Add Button"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :table-config="customConfig"
        @add="handleCustomAdd"
      />
    </div>

    <!-- Example 3: Success Variant Add Button -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">3. Success Variant Add Button</h2>
      <p class="text-gray-600 mb-4">Green Add button for positive actions</p>
      
      <SmartDataTable 
        title="Users with Success Add Button"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :table-config="successConfig"
        @add="handleSuccessAdd"
      />
    </div>

    <!-- Example 4: Large Add Button -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">4. Large Add Button</h2>
      <p class="text-gray-600 mb-4">Larger Add button for emphasis</p>
      
      <SmartDataTable 
        title="Users with Large Add Button"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :table-config="largeConfig"
        @add="handleLargeAdd"
      />
    </div>

    <!-- Example 5: Disabled Add Button -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">5. No Add Button</h2>
      <p class="text-gray-600 mb-4">Table without Add button (disabled)</p>
      
      <SmartDataTable 
        title="Users without Add Button"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :table-config="disabledConfig"
      />
    </div>

    <!-- Configuration Examples -->
    <div class="bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Configuration Examples</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="font-medium mb-2">Basic Configuration:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(basicConfig.addButton, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Custom Configuration:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(customConfig.addButton, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Success Configuration:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(successConfig.addButton, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Large Configuration:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(largeConfig.addButton, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import { toast } from '@/plugins/toast'

const userStore = useUserStore()

// Configuration Examples
const basicConfig = {
  addButton: {
    enabled: true
    // Uses all defaults: text="Add New", variant="primary", size="md"
  }
}

const customConfig = {
  addButton: {
    enabled: true,
    text: "Create New User",
    variant: "info",
    size: "md"
  }
}

const successConfig = {
  addButton: {
    enabled: true,
    text: "Add User",
    variant: "success",
    size: "md"
  }
}

const largeConfig = {
  addButton: {
    enabled: true,
    text: "Create User",
    variant: "primary",
    size: "lg"
  }
}

const disabledConfig = {
  addButton: {
    enabled: false
  }
}

// Event Handlers
function handleBasicAdd() {
  toast.info('Basic Add button clicked!')
  console.log('Basic Add button clicked')
  // Navigate to create user page or open modal
  // router.push('/admin/users/create')
}

function handleCustomAdd() {
  toast.info('Custom Add button clicked!')
  console.log('Custom Add button clicked')
  // Your custom add logic here
}

function handleSuccessAdd() {
  toast.success('Success Add button clicked!')
  console.log('Success Add button clicked')
  // Your success add logic here
}

function handleLargeAdd() {
  toast.info('Large Add button clicked!')
  console.log('Large Add button clicked')
  // Your large add logic here
}
</script>
