<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <TransitionGroup name="toast" tag="div" class="space-y-2">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="[
            'px-6 py-3 rounded-lg shadow-lg flex items-center min-w-80 max-w-md',
            'transform transition-all duration-300 ease-out',
            getToastClasses(toast.type)
          ]"
          @click="removeToast(toast.id)"
        >
          <!-- Icon -->
          <div class="flex-shrink-0 mr-3">
            <component :is="getIcon(toast.type)" class="w-6 h-6" />
          </div>

          <!-- Content -->
          <div class="flex-1 min-w-0">
            <p v-if="toast.title" class="font-semibold text-sm">{{ toast.title }}</p>
            <p class="text-sm" :class="{ 'mt-1': toast.title }">{{ toast.message }}</p>
          </div>

          <!-- Close Button -->
          <button
            v-if="toast.closable !== false"
            @click.stop="removeToast(toast.id)"
            class="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-black/10 transition-colors"
          >
            <XMarkIcon class="w-4 h-4" />
          </button>

          <!-- Progress Bar -->
          <div
            v-if="toast.duration && toast.duration > 0"
            class="absolute bottom-0 left-0 h-1 bg-black/20 rounded-b-lg transition-all duration-100 ease-linear"
            :style="{ width: `${getProgress(toast)}%` }"
          ></div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'
import { useToastStore } from '@/stores/toast'

const toastStore = useToastStore()

const toasts = computed(() => toastStore.toasts)

// Toast type configurations
const toastConfig = {
  success: {
    classes: 'bg-green-500 text-white',
    icon: CheckCircleIcon
  },
  error: {
    classes: 'bg-red-500 text-white',
    icon: ExclamationCircleIcon
  },
  warning: {
    classes: 'bg-yellow-500 text-white',
    icon: ExclamationTriangleIcon
  },
  info: {
    classes: 'bg-blue-500 text-white',
    icon: InformationCircleIcon
  },
  default: {
    classes: 'bg-gray-800 text-white',
    icon: InformationCircleIcon
  }
}

function getToastClasses(type) {
  return toastConfig[type]?.classes || toastConfig.default.classes
}

function getIcon(type) {
  return toastConfig[type]?.icon || toastConfig.default.icon
}

function getProgress(toast) {
  if (!toast.duration || toast.duration <= 0) return 0
  const elapsed = Date.now() - toast.createdAt
  const progress = Math.max(0, Math.min(100, (elapsed / toast.duration) * 100))
  return 100 - progress
}

function removeToast(id) {
  toastStore.removeToast(id)
}
</script>

<style scoped>
/* Toast animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease-out;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-move {
  transition: transform 0.3s ease-out;
}

/* Hover effects */
.toast-notification:hover {
  transform: translateX(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Progress bar animation */
@keyframes progress {
  from { width: 100%; }
  to { width: 0%; }
}
</style>
