import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],
        totalPages: 1,
        totalRecords: 0,
        loading: false,
    }),

    actions: {
        async fetchUser(params = {}) {
            this.loading = true;
            try {
                // Extract parameters with defaults and ensure proper types
                const {
                    page = 1,
                    per_page = 10,
                    search = '',
                    sort_field = null,
                    sort_direction = null,
                    ...filters
                } = params;

                // Convert to proper types to avoid Laravel errors
                const pageNum = parseInt(page) || 1;
                const limitNum = parseInt(per_page) || 10;

                // Build query parameters
                let url = `/user-list?page=${pageNum}&limit=${limitNum}`;

                // Add search if provided
                if (search && search.trim()) {
                    url += `&search=${encodeURIComponent(search.trim())}`;
                }

                // Add sorting if provided
                if (sort_field && sort_direction) {
                    url += `&sort=${sort_field}&direction=${sort_direction}`;
                    console.log(`Adding sort parameters: sort=${sort_field}, direction=${sort_direction}`);
                }

                // Add filters if provided
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        url += `&filter[${key}]=${encodeURIComponent(value)}`;
                    }
                });
                const response = await api.get(url);

                // Update store state
                this.user = response.data.data.data || [];
                this.totalPages = response.data.data.last_page || 1;
                this.totalRecords = response.data.data.total || 0;

                // Return data in the format expected by UserList.vue
                return {
                    data: this.user,
                    current_page: response.data.data.current_page || pageNum,
                    per_page: response.data.data.per_page || limitNum,
                    total: response.data.data.total || 0,
                    last_page: response.data.data.last_page || 1
                };
            } catch (err) {
                console.error("Error fetching users:", err);
                toast.error("Failed to fetch users");
                this.user = [];

                // Return empty data structure on error
                return {
                    data: [],
                    current_page: 1,
                    per_page: 10,
                    total: 0,
                    last_page: 1
                };
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods as they are
        async createUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/createUser", userData);
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating user:", err);
                toast.error(err.response?.data?.message || "Failed to create user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/editUser", userData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the user list to get updated data
                    await this.fetchUser();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating user:", err);
                toast.error(err.response?.data?.message || "Failed to update user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async deleteUser(userId) {
            this.loading = true;
            try {
                const response = await api.delete(`/deleteUser/${userId}`);

                if (response.data) {
                    toast.success(response.data.message || "User deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting user:", err);
                toast.error(err.response?.data?.message || "Failed to delete user");
                throw err; // Re-throw to let the component handle it
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods unchanged
    },
});

export default useUserStore;
