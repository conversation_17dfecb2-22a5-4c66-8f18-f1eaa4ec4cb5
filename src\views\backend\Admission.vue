<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import useAdmissionStore from '../../stores/admission'

const admissionStore = useAdmissionStore();
admissionStore.fetchAdmissions();
const pagination = ref({
    current_page: 1,
    per_page: 10,
    total: 0,
    last_page: 1
})
// Search and filter states
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref('asc');
const admissions = ref([])
const loading = ref(false)
async function Getadmissions(page = 1) {
    loading.value = true
    try {
        const params = {
            page: page,
            per_page: pagination.value.per_page,
            search: searchQuery.value,
            sort_field: sortField.value,
            sort_direction: sortDirection.value
        }

        const response = await admissionStore.fetchAdmissions(params);
        
        // // Update data and pagination
        admissions.value = response.data || []
        pagination.value = {
            current_page: response.current_page || 1,
            per_page: response.per_page || 10,
            total: response.total || 0,
            last_page: response.last_page || 1
        }

    } catch (error) {
        console.error('Failed to fetch admissions:', error)
        admissions.value = []
    } finally {
        loading.value = false
    }
}
onMounted(() => {
    Getadmissions();
})
</script>
<template>

</template>
