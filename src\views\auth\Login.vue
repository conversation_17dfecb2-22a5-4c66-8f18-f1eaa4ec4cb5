<script setup>
import { ref } from 'vue'

import { useRouter } from 'vue-router'

import useAuthStore from '@/stores/auth'

const router = useRouter();
const email = ref('');
const password = ref('');
const showPassword = ref(false);
const rememberMe = ref(false);
const isLoading = ref(false);

async function loginData() {
  isLoading.value = true;

  try {
    const isLoggedin = await useAuthStore().login({
      email: email.value,
      password: password.value
    });

    if (isLoggedin) {
      router.push('/admin/users');
    }
  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <div class="login-container">
    <div class="login-background">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
    </div>

    <div class="login-card">
      <div class="login-header">
        <img src="/img/sabyata_logo_2.png" alt="Sabhyata Little Star" class="login-logo">
        <h1>Admin Portal</h1>
      </div>

      <form @submit.prevent="loginData" class="login-form">
        <h2>Sign in to your account</h2>

        <div class="form-group">
          <label for="email">Email</label>
          <div class="input-wrapper">
            <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <input v-model="email" id="email" type="email" placeholder="<EMAIL>" required>
          </div>
        </div>

        <div class="form-group">
          <div class="label-row">
            <label for="password">Password</label>
            <a href="#" class="forgot-link">Forgot password?</a>
          </div>
          <div class="input-wrapper">
            <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
            <input v-model="password" id="password" :type="showPassword ? 'text' : 'password'" placeholder="••••••••"
              required>
            <button type="button" class="password-toggle" @click="showPassword = !showPassword">
              <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
            </button>
          </div>
        </div>

        <div class="remember-me">
          <label class="checkbox-container">
            <input v-model="rememberMe" type="checkbox">
            <span class="checkmark"></span>
            <span>Remember me</span>
          </label>
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          <span v-if="!isLoading">Sign In</span>
          <div v-else class="spinner"></div>
        </button>
      </form>

      <div class="login-footer">
        <div class="security-badge">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <span>Secure login</span>
        </div>
        <p class="copyright">© 2023 Sabhyata Little Star. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  background-color: #f9fafb;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.shape-1 {
  background: rgba(125, 211, 252, 0.4);
  width: 500px;
  height: 500px;
  top: -250px;
  left: -100px;
}

.shape-2 {
  background: rgba(167, 139, 250, 0.3);
  width: 400px;
  height: 400px;
  bottom: -200px;
  right: -100px;
}

.shape-3 {
  background: rgba(251, 146, 60, 0.2);
  width: 300px;
  height: 300px;
  bottom: 100px;
  left: 20%;
}

.shape-4 {
  background: rgba(52, 211, 153, 0.2);
  width: 200px;
  height: 200px;
  top: 10%;
  right: 20%;
}

.login-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.01);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
}

.login-header {
  padding: 30px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.login-logo {
  height: 60px;
  margin-bottom: 10px;
}

.login-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.login-form {
  padding: 30px;
}

.login-form h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 24px 0;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 8px;
}

.forgot-link {
  font-size: 14px;
  color: #6366f1;
  text-decoration: none;
  transition: color 0.2s;
}

.forgot-link:hover {
  color: #4f46e5;
  text-decoration: underline;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #9ca3af;
}

input[type="email"],
input[type="password"],
input[type="text"] {
  width: 100%;
  padding: 14px 14px 14px 44px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  color: #1e293b;
  background: white;
  transition: all 0.2s;
}

input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.password-toggle {
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9ca3af;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
}

.remember-me {
  margin-bottom: 24px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #4b5563;
  user-select: none;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  height: 18px;
  width: 18px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  margin-right: 10px;
  transition: all 0.2s;
}

.checkbox-container:hover input~.checkmark {
  border-color: #6366f1;
}

.checkbox-container input:checked~.checkmark {
  background-color: #6366f1;
  border-color: #6366f1;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked~.checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.login-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-button:hover {
  background: linear-gradient(to right, #4f46e5, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.2);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.login-footer {
  padding: 20px 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
}

.security-badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f3f4f6;
  border-radius: 20px;
  margin-bottom: 16px;
}

.security-badge svg {
  width: 16px;
  height: 16px;
  color: #6366f1;
  margin-right: 8px;
}

.security-badge span {
  font-size: 14px;
  color: #4b5563;
}

.copyright {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

@media (max-width: 500px) {
  .login-card {
    border-radius: 16px;
  }

  .login-header,
  .login-form,
  .login-footer {
    padding: 20px;
  }

  .login-form h2 {
    font-size: 20px;
  }

  input[type="email"],
  input[type="password"],
  input[type="text"],
  .login-button {
    padding: 12px;
    font-size: 14px;
  }
}
</style>