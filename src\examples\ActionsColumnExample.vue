<template>
  <div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold text-gray-900">Actions Column Examples</h1>
    
    <!-- Example 1: Auto Actions (Recommended) -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">1. Auto Actions (Recommended)</h2>
      <p class="text-gray-600 mb-4">Actions auto-added based on event handlers (@edit, @view)</p>
      
      <SmartDataTable 
        title="Users - Auto Actions"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :column-config="autoActionsConfig"
        @edit="handleEdit"
        @view="handleView"
        @add="handleAdd"
      />
    </div>

    <!-- Example 2: Custom Actions -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">2. Custom Actions Configuration</h2>
      <p class="text-gray-600 mb-4">Explicitly defined actions with custom styling</p>
      
      <SmartDataTable 
        title="Users - Custom Actions"
        :store="userStore"
        fetch-method="fetchUser"
        delete-method="deleteUser"
        :column-config="customActionsConfig"
        @edit="handleEdit"
        @view="handleView"
        @add="handleAdd"
      />
    </div>

    <!-- Example 3: Limited Actions -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">3. Limited Actions</h2>
      <p class="text-gray-600 mb-4">Only view action, no edit or delete</p>
      
      <SmartDataTable 
        title="Users - View Only"
        :store="userStore"
        fetch-method="fetchUser"
        :column-config="limitedActionsConfig"
        @view="handleView"
        @add="handleAdd"
      />
    </div>

    <!-- Example 4: No Actions -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">4. No Actions Column</h2>
      <p class="text-gray-600 mb-4">Read-only table with no action buttons</p>
      
      <SmartDataTable 
        title="Users - Read Only"
        :store="userStore"
        fetch-method="fetchUser"
        :column-config="noActionsConfig"
        @add="handleAdd"
      />
    </div>

    <!-- Configuration Examples -->
    <div class="bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Configuration Examples</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="font-medium mb-2">Auto Actions (Recommended):</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(autoActionsConfig, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Custom Actions:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(customActionsConfig.actions, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">Limited Actions:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(limitedActionsConfig.actions, null, 2) }}</pre>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">No Actions:</h3>
          <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(noActionsConfig, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import { toast } from '@/plugins/toast'

const userStore = useUserStore()

// Base columns for all examples
const baseColumns = {
  id: { 
    title: 'ID',
    width: '80px',
    filterable: true
  },
  name: { 
    title: 'Full Name',
    filterable: true
  },
  email: { 
    title: 'Email',
    filterable: true
  },
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 1, label: 'Active' },
      { value: 0, label: 'Inactive' }
    ]
  }
}

// Example 1: Auto Actions (Recommended)
// Actions column auto-added based on @edit, @view handlers
const autoActionsConfig = {
  ...baseColumns
  // No actions defined - SmartDataTable will auto-add based on event handlers
}

// Example 2: Custom Actions
const customActionsConfig = {
  ...baseColumns,
  actions: {
    title: 'Actions',
    type: 'actions',
    width: '150px',
    sortable: false,
    filterable: false,
    actions: [
      {
        name: 'view',
        icon: 'eye',
        variant: 'info',
        title: 'View Details'
      },
      {
        name: 'edit',
        icon: 'pencil',
        variant: 'warning',
        title: 'Edit User'
      },
      {
        name: 'delete',
        icon: 'trash',
        variant: 'danger',
        title: 'Delete User'
      }
    ]
  }
}

// Example 3: Limited Actions
const limitedActionsConfig = {
  ...baseColumns,
  actions: {
    title: 'Actions',
    type: 'actions',
    width: '100px',
    sortable: false,
    filterable: false,
    actions: ['view']  // Only view action
  }
}

// Example 4: No Actions
const noActionsConfig = {
  ...baseColumns
  // No actions column - read-only table
}

// Event Handlers
function handleEdit(user) {
  toast.info(`Edit user: ${user.name}`)
  console.log('Edit user:', user)
}

function handleView(user) {
  toast.info(`View user: ${user.name}`)
  console.log('View user:', user)
}

function handleAdd() {
  toast.success('Add new user!')
  console.log('Add new user')
}
</script>
