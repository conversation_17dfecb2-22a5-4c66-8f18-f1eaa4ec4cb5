<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartDataTable Documentation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e40af;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 10px;
        }
        h2 {
            color: #1e40af;
            margin-top: 40px;
            border-left: 4px solid #3b82f6;
            padding-left: 15px;
        }
        h3 {
            color: #374151;
            margin-top: 30px;
        }
        .highlight {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
            margin: 20px 0;
        }
        .success {
            background: #d1fae5;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        pre[class*="language-"] {
            margin: 15px 0;
            border-radius: 8px;
            font-size: 14px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre;
        }
        .inline-code {
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #374151;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #d1d5db;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f3f4f6;
            font-weight: 600;
        }
        .example-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            background: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 10px 0;
        }
        .warning {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SmartDataTable Documentation</h1>
        
        <div class="success">
            <strong>✅ Problem Solved!</strong> Reduce your Vue DataTable code from 280+ lines to just ~50 lines while automating all repetitive functionality.
        </div>

        <h2>📋 Table of Contents</h2>
        <ul>
            <li><a href="#overview">Overview & Benefits</a></li>
            <li><a href="#store-format">Store Format Requirements</a></li>
            <li><a href="#basic-usage">Basic Usage</a></li>
            <li><a href="#examples">Real Examples (Users, Admissions, Posts)</a></li>
            <li><a href="#customization">Customization Options</a></li>
            <li><a href="#migration">Migration Guide</a></li>
        </ul>

        <h2 id="overview">🎯 Overview & Benefits</h2>
        
        <div class="highlight">
            <strong>Before:</strong> 280+ lines of repetitive code for each data table<br>
            <strong>After:</strong> ~50 lines with full automation
        </div>

        <h3>What SmartDataTable Handles Automatically:</h3>
        <ul>
            <li>✅ Column generation from data structure</li>
            <li>✅ Server-side pagination, sorting, filtering</li>
            <li>✅ CRUD operations (Create, Read, Update, Delete)</li>
            <li>✅ Loading states and error handling</li>
            <li>✅ Export functionality (CSV, Excel)</li>
            <li>✅ Responsive design</li>
        </ul>

        <h2 id="store-format">🏪 Store Format Requirements</h2>
        
        <div class="warning">
            <strong>Important:</strong> Your store must follow this exact format for SmartDataTable to work properly.
        </div>

        <h3>Required Store Structure:</h3>
        
        <pre><code class="language-javascript">// Example: User Store
const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],           // ← Data array (can be any name: user, admissions, posts, etc.)
        totalPages: 1,      // ← Required for pagination
        totalRecords: 0,    // ← Required for pagination info
        loading: false,     // ← Optional but recommended
    }),

    actions: {
        // Required: Fetch method with these exact parameters
        async fetchUser(page = 1, limit = 10, sortBy = null, sortDir = null, filters = null) {
            this.loading = true;
            try {
                // Build your API URL
                let url = `/user-list?page=${page}&limit=${limit}`;

                // Add sorting if provided
                if (sortBy && sortDir) {
                    url += `&sort=${sortBy}&direction=${sortDir}`;
                }

                // Add filters if provided
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            url += `&filter[${key}]=${value}`;
                        }
                    });
                }

                const response = await api.get(url);

                // Update state - IMPORTANT: Use exact property names
                this.user = response.data.data.data;           // ← Your data array
                this.totalPages = response.data.data.total_pages || 1;
                this.totalRecords = response.data.data.total || 0;

                return this.user;
            } catch (err) {
                console.error("Error fetching data:", err);
                this.user = [];
            } finally {
                this.loading = false;
            }
        },

        // Required: Delete method
        async deleteUser(id) {
            this.loading = true;
            try {
                const response = await api.delete(`/deleteUser/${id}`);
                if (response.data) {
                    // Remove from local state
                    this.user = this.user.filter((item) => item.id !== id);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting:", err);
                return false;
            } finally {
                this.loading = false;
            }
        }
    }
});</code></pre>

        <h2 id="basic-usage">🚀 Basic Usage</h2>

        <h3>Minimal Implementation (Just 10 lines!):</h3>
        
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div class="p-4"&gt;
    &lt;SmartDataTable
      title="User Management"
      :store="userStore"
      fetch-method="fetchUser"
      delete-method="deleteUser"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useUserStore from '@/stores/user'

const userStore = useUserStore()
&lt;/script&gt;</code></pre>

        <div class="success">
            <strong>That's it!</strong> SmartDataTable will automatically:
            <ul>
                <li>Generate columns from your data structure</li>
                <li>Handle pagination, sorting, filtering</li>
                <li>Provide edit/delete actions</li>
                <li>Use optimal defaults for everything</li>
            </ul>
        </div>

        <h2 id="examples">📚 Real Examples</h2>

        <h3>1. Users Management</h3>
        <div class="example-box">
            <h4>Store (useUserStore):</h4>
            <div class="code-block">state: () => ({
    user: [],           // ← Array of user objects
    totalPages: 1,
    totalRecords: 0,
}),

actions: {
    async fetchUser(page, limit, sortBy, sortDir, filters) { /* ... */ },
    async deleteUser(id) { /* ... */ }
}</div>

            <h4>Vue Component:</h4>
            <div class="code-block">&lt;SmartDataTable 
  title="User Management"
  :store="userStore"
  fetch-method="fetchUser"
  delete-method="deleteUser"
/&gt;</div>
        </div>

        <h3>2. Admissions Management</h3>
        <div class="example-box">
            <h4>Store (useAdmissionStore):</h4>
            <div class="code-block">state: () => ({
    admissions: [],     // ← Array of admission objects
    totalPages: 1,
    totalRecords: 0,
}),

actions: {
    async fetchAdmissions(page, limit, sortBy, sortDir, filters) {
        // Your API call logic
        let url = `/admissions?page=${page}&limit=${limit}`;
        // ... rest of implementation
        this.admissions = response.data.data.data;
        this.totalPages = response.data.data.total_pages;
        this.totalRecords = response.data.data.total;
    },
    
    async deleteAdmission(id) {
        // Your delete logic
        await api.delete(`/admissions/${id}`);
        this.admissions = this.admissions.filter(item => item.id !== id);
    }
}</div>

            <h4>Vue Component:</h4>
            <div class="code-block">&lt;SmartDataTable 
  title="Admission Management"
  :store="admissionStore"
  fetch-method="fetchAdmissions"
  delete-method="deleteAdmission"
/&gt;</div>
        </div>

        <h3>3. Posts Management</h3>
        <div class="example-box">
            <h4>Store (usePostStore):</h4>
            <div class="code-block">state: () => ({
    posts: [],          // ← Array of post objects
    totalPages: 1,
    totalRecords: 0,
}),

actions: {
    async fetchPosts(page, limit, sortBy, sortDir, filters) { /* ... */ },
    async deletePost(id) { /* ... */ }
}</div>

            <h4>Vue Component:</h4>
            <div class="code-block">&lt;SmartDataTable 
  title="Post Management"
  :store="postStore"
  fetch-method="fetchPosts"
  delete-method="deletePost"
/&gt;</div>
        </div>

        <h2 id="customization">⚙️ Customization Options</h2>

        <h3>Column Configuration:</h3>
        <div class="code-block">const columnConfig = {
  id: { width: '80px' },
  name: { title: 'Full Name', width: '200px' },
  email: { title: 'Email Address' },
  status: { 
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  },
  created_at: { title: 'Created Date', type: 'date' }
}</div>

        <h3>Table Configuration:</h3>
        <div class="code-block">const tableConfig = {
  pagination: {
    pageSize: 15,
    pageSizes: [10, 15, 25, 50]
  },
  export: {
    enabled: true,
    formats: ['csv', 'excel']
  }
}</div>

        <h3>Complete Example with Customization:</h3>
        <div class="code-block">&lt;SmartDataTable 
  title="User Management"
  :store="userStore"
  fetch-method="fetchUser"
  delete-method="deleteUser"
  :column-config="columnConfig"
  :table-config="tableConfig"
  :default-actions="['view', 'edit', 'delete']"
  @edit="handleEdit"
  @view="handleView"
/&gt;</div>

        <h2>🔄 Auto-Column Generation Rules</h2>
        
        <table>
            <thead>
                <tr>
                    <th>Field Pattern</th>
                    <th>Auto Type</th>
                    <th>Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Contains 'email'</td>
                    <td>email</td>
                    <td>user_email, email_address</td>
                </tr>
                <tr>
                    <td>Contains 'date' or 'time'</td>
                    <td>date</td>
                    <td>created_at, birth_date</td>
                </tr>
                <tr>
                    <td>Field name is 'status'</td>
                    <td>badge</td>
                    <td>status</td>
                </tr>
                <tr>
                    <td>Contains 'image', 'avatar', 'photo'</td>
                    <td>image</td>
                    <td>profile_image, avatar_url</td>
                </tr>
                <tr>
                    <td>Number value</td>
                    <td>number</td>
                    <td>age, price, count</td>
                </tr>
                <tr>
                    <td>Boolean value</td>
                    <td>boolean</td>
                    <td>is_active, verified</td>
                </tr>
                <tr>
                    <td>Everything else</td>
                    <td>text</td>
                    <td>name, description</td>
                </tr>
            </tbody>
        </table>

        <div class="highlight">
            <strong>Note:</strong> Fields <span class="inline-code">created_at</span>, <span class="inline-code">updated_at</span>, <span class="inline-code">deleted_at</span> are automatically hidden.
        </div>

        <h2 id="migration">🔄 Migration Guide</h2>

        <div class="step">
            <h4>Step 1: Update Your Store</h4>
            Ensure your store follows the required format with proper method signatures and state properties.
        </div>

        <div class="step">
            <h4>Step 2: Replace Your Vue Component</h4>
            Replace your existing 280+ line DataTable implementation with the SmartDataTable component.
        </div>

        <div class="step">
            <h4>Step 3: Test & Customize</h4>
            Test the auto-generated columns and add custom configurations only where needed.
        </div>

        <h3>Props Reference:</h3>
        <table>
            <thead>
                <tr>
                    <th>Prop</th>
                    <th>Type</th>
                    <th>Default</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>title</td>
                    <td>String</td>
                    <td>''</td>
                    <td>Table title</td>
                </tr>
                <tr>
                    <td>store</td>
                    <td>Object</td>
                    <td>Required</td>
                    <td>Pinia store instance</td>
                </tr>
                <tr>
                    <td>fetch-method</td>
                    <td>String</td>
                    <td>'fetchUser'</td>
                    <td>Store method for fetching data</td>
                </tr>
                <tr>
                    <td>delete-method</td>
                    <td>String</td>
                    <td>'deleteUser'</td>
                    <td>Store method for deleting data</td>
                </tr>
                <tr>
                    <td>column-config</td>
                    <td>Object</td>
                    <td>{}</td>
                    <td>Custom column configurations</td>
                </tr>
                <tr>
                    <td>table-config</td>
                    <td>Object</td>
                    <td>{}</td>
                    <td>Custom table configurations</td>
                </tr>
                <tr>
                    <td>default-actions</td>
                    <td>Array</td>
                    <td>['edit', 'delete']</td>
                    <td>Default CRUD actions</td>
                </tr>
                <tr>
                    <td>auto-columns</td>
                    <td>Boolean</td>
                    <td>true</td>
                    <td>Auto-generate columns</td>
                </tr>
                <tr>
                    <td>auto-load</td>
                    <td>Boolean</td>
                    <td>true</td>
                    <td>Auto-load data on mount</td>
                </tr>
            </tbody>
        </table>

        <h2>🎓 Complete Admissions Example</h2>

        <div class="example-box">
            <h3>Step 1: Create/Update Admissions Store</h3>
            <div class="code-block">// stores/admission/index.js
import { defineStore } from 'pinia'
import { toast } from '@/plugins/toast'
import api from '@/api/api'

const useAdmissionStore = defineStore("useAdmissionStore", {
    state: () => ({
        admissions: [],      // ← Main data array
        totalPages: 1,       // ← Required for pagination
        totalRecords: 0,     // ← Required for pagination info
        loading: false,
    }),

    actions: {
        // Required method with exact signature
        async fetchAdmissions(page = 1, limit = 10, sortBy = null, sortDir = null, filters = null) {
            this.loading = true;
            try {
                // Build query parameters
                let url = `/admissions?page=${page}&limit=${limit}`;

                // Add sorting if provided
                if (sortBy && sortDir) {
                    url += `&sort=${sortBy}&direction=${sortDir}`;
                }

                // Add filters if provided
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            url += `&filter[${key}]=${value}`;
                        }
                    });
                }

                const response = await api.get(url);

                // Update state with exact property names
                this.admissions = response.data.data.data;
                this.totalPages = response.data.data.total_pages || 1;
                this.totalRecords = response.data.data.total || 0;

                return this.admissions;
            } catch (err) {
                console.error("Error fetching admissions:", err);
                toast.error("Failed to fetch admissions");
                this.admissions = [];
            } finally {
                this.loading = false;
            }
        },

        // Required delete method
        async deleteAdmission(id) {
            this.loading = true;
            try {
                const response = await api.delete(`/admissions/${id}`);
                if (response.data) {
                    // Remove from local state
                    this.admissions = this.admissions.filter((admission) => admission.id !== id);
                    toast.success("Admission deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting admission:", err);
                toast.error("Failed to delete admission");
                return false;
            } finally {
                this.loading = false;
            }
        }
    }
});

export default useAdmissionStore;</div>

            <h3>Step 2: Create Admissions Vue Component</h3>
            <div class="code-block">// views/backend/AdmissionList.vue
&lt;template&gt;
  &lt;div class="p-4"&gt;
    &lt;SmartDataTable
      title="Admission Management"
      :store="admissionStore"
      fetch-method="fetchAdmissions"
      delete-method="deleteAdmission"
      :column-config="columnConfig"
      :default-actions="['view', 'edit', 'delete']"
      @edit="handleEdit"
      @view="handleView"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useAdmissionStore from '@/stores/admission'
import { useRouter } from 'vue-router'

const admissionStore = useAdmissionStore()
const router = useRouter()

// Optional: Custom column configurations
const columnConfig = {
  id: { width: '80px' },
  student_name: { title: 'Student Name', width: '200px' },
  email: { title: 'Email Address' },
  phone: { title: 'Phone Number' },
  course: { title: 'Applied Course' },
  status: {
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'pending', label: 'Pending' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'enrolled', label: 'Enrolled' }
    ]
  },
  application_date: { title: 'Application Date', type: 'date' }
}

// Handle custom actions
function handleEdit(admission) {
  router.push(`/admin/admissions/edit/${admission.id}`)
}

function handleView(admission) {
  router.push(`/admin/admissions/view/${admission.id}`)
}
&lt;/script&gt;</div>

            <h3>Step 3: Update Your Store Import (if needed)</h3>
            <div class="code-block">// Update the SmartDataTable composable to recognize 'admissions' data
// This is already handled automatically - the composable looks for:
// store.data || store.user || store.admissions || store.posts || etc.</div>
        </div>

        <h2>🔧 Store Data Property Names</h2>

        <div class="highlight">
            <strong>Important:</strong> The SmartDataTable automatically detects your data array. It looks for these property names in order:
        </div>

        <table>
            <thead>
                <tr>
                    <th>Store Property</th>
                    <th>Use Case</th>
                    <th>Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="inline-code">data</span></td>
                    <td>Generic data</td>
                    <td>Any data type</td>
                </tr>
                <tr>
                    <td><span class="inline-code">user</span></td>
                    <td>User management</td>
                    <td>User lists</td>
                </tr>
                <tr>
                    <td><span class="inline-code">admissions</span></td>
                    <td>Admission management</td>
                    <td>Student admissions</td>
                </tr>
                <tr>
                    <td><span class="inline-code">posts</span></td>
                    <td>Content management</td>
                    <td>Blog posts, articles</td>
                </tr>
                <tr>
                    <td><span class="inline-code">products</span></td>
                    <td>E-commerce</td>
                    <td>Product catalogs</td>
                </tr>
            </tbody>
        </table>

        <div class="success">
            <h3>🎉 Result</h3>
            <p>With SmartDataTable, you can create fully functional data tables with just a few lines of code, while maintaining full customization capabilities when needed. No more repetitive boilerplate code!</p>

            <h4>What You Get:</h4>
            <ul>
                <li>✅ 90% less code (280+ lines → ~50 lines)</li>
                <li>✅ Automatic column generation</li>
                <li>✅ Server-side pagination, sorting, filtering</li>
                <li>✅ CRUD operations out of the box</li>
                <li>✅ Responsive design</li>
                <li>✅ Export functionality</li>
                <li>✅ Reusable across all data types</li>
            </ul>
        </div>

        <div class="highlight">
            <strong>💡 Pro Tip:</strong> Start with the minimal implementation and only add customizations when you need to override the smart defaults. The component is designed to work perfectly with zero configuration!
        </div>

        <div class="warning">
            <strong>⚠️ Remember:</strong> Your store must have the exact method signatures shown above for the SmartDataTable to work properly. The fetch method must accept <span class="inline-code">(page, limit, sortBy, sortDir, filters)</span> parameters.
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
