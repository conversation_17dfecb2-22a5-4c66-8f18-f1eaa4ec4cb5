# SmartDataTable Quick Start Guide

## 🚀 For Any New Data Type (Users, Admissions, Posts, etc.)

### Step 1: Create Store with Exact Format

```javascript
// stores/[datatype]/index.js
import { defineStore } from 'pinia'
import { toast } from '@/plugins/toast'
import api from '@/api/api'

const use[DataType]Store = defineStore("use[DataType]Store", {
    state: () => ({
        [datatype]: [],      // ← Your data array (users, admissions, posts, etc.)
        totalPages: 1,       // ← Required for pagination
        totalRecords: 0,     // ← Required for pagination info
        loading: false,
    }),

    actions: {
        // Required method with EXACT signature
        async fetch[DataType](page = 1, limit = 10, sortBy = null, sortDir = null, filters = null) {
            this.loading = true;
            try {
                // Build query parameters
                let url = `/[endpoint]?page=${page}&limit=${limit}`;

                // Add sorting if provided
                if (sortBy && sortDir) {
                    url += `&sort=${sortBy}&direction=${sortDir}`;
                }

                // Add filters if provided
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            url += `&filter[${key}]=${value}`;
                        }
                    });
                }

                const response = await api.get(url);
                
                // Update state - IMPORTANT: Use exact property names
                this.[datatype] = response.data.data.data;
                this.totalPages = response.data.data.total_pages || 1;
                this.totalRecords = response.data.data.total || 0;

                return this.[datatype];
            } catch (err) {
                console.error("Error fetching data:", err);
                toast.error("Failed to fetch data");
                this.[datatype] = [];
            } finally {
                this.loading = false;
            }
        },

        // Required delete method
        async delete[DataType](id) {
            this.loading = true;
            try {
                const response = await api.delete(`/[endpoint]/${id}`);
                if (response.data) {
                    this.[datatype] = this.[datatype].filter((item) => item.id !== id);
                    toast.success("Deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting:", err);
                toast.error("Failed to delete");
                return false;
            } finally {
                this.loading = false;
            }
        }
    }
});

export default use[DataType]Store;
```

### Step 2: Create Vue Component (Just ~10 lines!)

```vue
<template>
  <div class="p-4">
    <SmartDataTable 
      title="[DataType] Management"
      :store="[datatype]Store"
      fetch-method="fetch[DataType]"
      delete-method="delete[DataType]"
    />
  </div>
</template>

<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import use[DataType]Store from '@/stores/[datatype]'

const [datatype]Store = use[DataType]Store()
</script>
```

## 📋 Real Examples

### For Users:
```javascript
// Store: useUserStore
state: { user: [], totalPages: 1, totalRecords: 0 }
methods: fetchUser(), deleteUser()

// Component:
<SmartDataTable 
  title="User Management"
  :store="userStore"
  fetch-method="fetchUser"
  delete-method="deleteUser"
/>
```

### For Admissions:
```javascript
// Store: useAdmissionStore  
state: { admissions: [], totalPages: 1, totalRecords: 0 }
methods: fetchAdmissions(), deleteAdmission()

// Component:
<SmartDataTable 
  title="Admission Management"
  :store="admissionStore"
  fetch-method="fetchAdmissions"
  delete-method="deleteAdmission"
/>
```

### For Posts:
```javascript
// Store: usePostStore
state: { posts: [], totalPages: 1, totalRecords: 0 }
methods: fetchPosts(), deletePost()

// Component:
<SmartDataTable 
  title="Post Management"
  :store="postStore"
  fetch-method="fetchPosts"
  delete-method="deletePost"
/>
```

## ⚙️ Optional Customizations

### Custom Column Configuration:
```javascript
const columnConfig = {
  id: { width: '80px' },
  name: { title: 'Full Name' },
  status: { 
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  }
}
```

### Custom Actions:
```vue
<SmartDataTable 
  :default-actions="['view', 'edit', 'delete']"
  @edit="handleEdit"
  @view="handleView"
/>
```

## 🎯 Key Points

1. **Store must have exact method signatures**: `fetch[Type](page, limit, sortBy, sortDir, filters)`
2. **State must include**: `[datatype]`, `totalPages`, `totalRecords`
3. **API response format**: `response.data.data.data` for array, `response.data.data.total_pages` for pagination
4. **SmartDataTable auto-detects**: data property name, column types, filters
5. **Zero configuration needed** - works out of the box!

## 🎨 New Enhanced Features

### **✨ Print & Export Selected Data**
- **Export selected rows** to CSV/Excel with dedicated button
- **Print selected data** with custom formatting
- **Bulk actions** for selected items
- **Smart selection** with visual feedback

### **🎭 Beautiful Animations & Transitions**
- **Loading animations** with spinning indicators and pulse effects
- **Hover effects** on rows, buttons, and cards
- **Smooth transitions** for all interactions
- **Enhanced refresh button** with spinning animation
- **Fade-in effects** for page loads
- **Scale animations** for action buttons

### **🎨 Modern Design Enhancements**
- **Gradient backgrounds** and modern card layouts
- **Enhanced shadows** and hover effects
- **Improved typography** with better spacing
- **Responsive design** optimized for all devices
- **Professional color schemes** with consistent branding

## 🚀 Result

- **280+ lines → ~50 lines** (90% reduction)
- **Auto-generated columns** from data structure
- **Server-side everything** handled internally
- **Reusable across all data types**
- **Fully customizable** when needed
- **✨ Beautiful animations** and modern design
- **📱 Fully responsive** with mobile optimization
- **🎯 Enhanced UX** with loading states and feedback

**Just follow the format above and you'll have a fully functional, beautiful data table in minutes!**
