<script setup>
import { provide } from 'vue'
import ConfirmationModal from './components/ConfirmationModal.vue'
import ToastNotification from './components/ToastNotification.vue'
import { confirmationState, useDataTableConfirmation } from './composables/useDataTableConfirmation'
import { toastState, useDataTableToast } from './composables/useDataTableToast'

// Provide the confirmation and toast functionality to child components
const { handleConfirm, handleCancel } = useDataTableConfirmation()
const toast = useDataTableToast()

// Provide to child components
provide('dataTableConfirmation', useDataTableConfirmation())
provide('dataTableToast', toast)
</script>

<template>
  <div class="datatable-provider">
    <!-- Slot for DataTable content -->
    <slot />
    
    <!-- Global Confirmation Modal for DataTable -->
    <ConfirmationModal 
      :show="confirmationState.show"
      :title="confirmationState.title"
      :message="confirmationState.message"
      :confirm-text="confirmationState.confirmText"
      :cancel-text="confirmationState.cancelText"
      :type="confirmationState.type"
      :loading="confirmationState.loading"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
    
    <!-- Global Toast Notifications for DataTable -->
    <ToastNotification />
  </div>
</template>

<style scoped>
.datatable-provider {
  position: relative;
}
</style>
