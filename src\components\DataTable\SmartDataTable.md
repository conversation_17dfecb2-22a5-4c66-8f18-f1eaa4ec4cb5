# SmartDataTable - Automated DataTable Solution

## Overview

SmartDataTable is a wrapper around the existing DataTable component that automatically handles:
- Column generation from data structure
- Server-side pagination, sorting, and filtering
- CRUD operations
- Minimal configuration required

## Benefits

✅ **Reduces Vue file code by 90%** - From 280 lines to ~50 lines
✅ **Auto-generates columns** from your data structure
✅ **Handles all server operations** internally
✅ **Smart defaults** for common use cases
✅ **Customizable** when needed
✅ **Reusable** across different data types

## Basic Usage

### 1. Simple Usage (Minimal Code)

```vue
<template>
  <div class="p-4">
    <SmartDataTable 
      title="User Management"
      :store="userStore"
      fetch-method="fetchUser"
      delete-method="deleteUser"
    />
  </div>
</template>

<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useUserStore from '@/stores/user'

const userStore = useUserStore()
</script>
```

That's it! The component will:
- Auto-generate columns from your data
- Handle pagination, sorting, filtering
- Provide edit/delete actions
- Use sensible defaults

### 2. With Custom Configuration

```vue
<template>
  <div class="p-4">
    <SmartDataTable 
      title="User Management"
      :store="userStore"
      fetch-method="fetchUser"
      delete-method="deleteUser"
      :column-config="columnConfig"
      :table-config="tableConfig"
      :default-actions="['view', 'edit', 'delete']"
      @edit="handleEdit"
      @view="handleView"
    />
  </div>
</template>

<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useUserStore from '@/stores/user'

const userStore = useUserStore()

// Optional: Override auto-generated columns
const columnConfig = {
  id: { width: '80px' },
  user_position: { title: 'Position' },
  status: { 
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  }
}

// Optional: Override table defaults
const tableConfig = {
  pagination: {
    pageSize: 15
  }
}

function handleEdit(user) {
  // Handle edit action
  router.push(`/admin/users/edit/${user.id}`)
}

function handleView(user) {
  // Handle view action
  router.push(`/admin/users/view/${user.id}`)
}
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | String | `''` | Table title |
| `store` | Object | Required | Pinia store instance |
| `fetchMethod` | String | `'fetchUser'` | Store method for fetching data |
| `deleteMethod` | String | `'deleteUser'` | Store method for deleting data |
| `columnConfig` | Object | `{}` | Custom column configurations |
| `tableConfig` | Object | `{}` | Custom table configurations |
| `defaultActions` | Array | `['edit', 'delete']` | Default CRUD actions |
| `autoColumns` | Boolean | `true` | Auto-generate columns |
| `autoLoad` | Boolean | `true` | Auto-load data on mount |

## Events

| Event | Description |
|-------|-------------|
| `@action` | Emitted for all actions |
| `@edit` | Emitted when edit action is clicked |
| `@view` | Emitted when view action is clicked |
| `@delete` | Emitted when delete action is clicked (handled automatically) |

## Column Auto-Generation Rules

The SmartDataTable automatically determines column types based on:

- **Number fields** → `number` type
- **Boolean fields** → `boolean` type  
- **Email fields** → `email` type (contains 'email')
- **Date fields** → `date` type (contains 'date' or 'time')
- **Status fields** → `badge` type with select filter
- **Image fields** → `image` type (contains 'image', 'avatar', 'photo')
- **Other fields** → `text` type

Automatically skips: `created_at`, `updated_at`, `deleted_at`

## Store Requirements

Your store should have methods that accept these parameters:

```javascript
// Fetch method
async fetchUser(page, pageSize, sortBy, sortDir, filters) {
  // Implementation
}

// Delete method  
async deleteUser(id) {
  // Implementation
}
```

Store should expose:
- `user` or `data` - Array of records
- `totalRecords` or `total` - Total count
- `totalPages` - Total pages

## Migration from Old DataTable

**Before (280 lines):**
```vue
<script setup>
// 200+ lines of boilerplate code
// Column definitions
// Event handlers
// State management
// etc.
</script>
```

**After (50 lines):**
```vue
<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useUserStore from '@/stores/user'

const userStore = useUserStore()
// Optional customizations only
</script>
```

## Examples for Different Data Types

### Products Table
```vue
<SmartDataTable 
  title="Product Management"
  :store="productStore"
  fetch-method="fetchProducts"
  delete-method="deleteProduct"
/>
```

### Orders Table
```vue
<SmartDataTable 
  title="Order Management"
  :store="orderStore"
  fetch-method="fetchOrders"
  delete-method="deleteOrder"
  :default-actions="['view', 'edit']"
/>
```

### Categories Table
```vue
<SmartDataTable 
  title="Category Management"
  :store="categoryStore"
  fetch-method="fetchCategories"
  delete-method="deleteCategory"
/>
```

The SmartDataTable handles everything automatically while still allowing customization when needed!
