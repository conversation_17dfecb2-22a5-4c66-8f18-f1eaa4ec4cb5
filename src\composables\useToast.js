import { useToastStore } from '@/stores/toast'

/**
 * Toast composable for easy toast management
 * Provides a simple API for showing toast notifications
 */
export function useToast() {
  const toastStore = useToastStore()

  /**
   * Show a success toast
   * @param {string} message - Success message
   * @param {Object} options - Additional options
   */
  const success = (message, options = {}) => {
    return toastStore.success(message, {
      duration: 3000,
      ...options
    })
  }

  /**
   * Show an error toast
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   */
  const error = (message, options = {}) => {
    return toastStore.error(message, {
      duration: 0, // Errors persist until manually closed
      ...options
    })
  }

  /**
   * Show a warning toast
   * @param {string} message - Warning message
   * @param {Object} options - Additional options
   */
  const warning = (message, options = {}) => {
    return toastStore.warning(message, {
      duration: 4000,
      ...options
    })
  }

  /**
   * Show an info toast
   * @param {string} message - Info message
   * @param {Object} options - Additional options
   */
  const info = (message, options = {}) => {
    return toastStore.info(message, {
      duration: 3000,
      ...options
    })
  }

  /**
   * Show a loading toast
   * @param {string} message - Loading message
   * @param {Object} options - Additional options
   */
  const loading = (message, options = {}) => {
    return toastStore.loading(message, options)
  }

  /**
   * Remove a specific toast
   * @param {number} id - Toast ID
   */
  const remove = (id) => {
    return toastStore.removeToast(id)
  }

  /**
   * Clear all toasts
   */
  const clear = () => {
    return toastStore.clearAllToasts()
  }

  /**
   * Update an existing toast
   * @param {number} id - Toast ID
   * @param {Object} updates - Updates to apply
   */
  const update = (id, updates) => {
    return toastStore.updateToast(id, updates)
  }

  /**
   * Handle promise with loading/success/error toasts
   * @param {Promise} promise - Promise to handle
   * @param {Object} messages - Custom messages
   */
  const promise = (promise, messages = {}) => {
    return toastStore.promise(promise, messages)
  }

  /**
   * Convenience method for API responses
   * @param {Promise} apiCall - API call promise
   * @param {Object} options - Options
   */
  const api = async (apiCall, options = {}) => {
    const {
      loadingMessage = 'Processing...',
      successMessage = 'Operation completed successfully!',
      errorMessage = 'Operation failed!',
      showSuccess = true,
      showError = true
    } = options

    const loadingId = loading(loadingMessage)

    try {
      const result = await apiCall
      remove(loadingId)

      if (showSuccess) {
        success(successMessage)
      }

      return result
    } catch (error) {
      remove(loadingId)

      if (showError) {
        const message = error?.response?.data?.message || error?.message || errorMessage
        error(message)
      }

      throw error
    }
  }

  /**
   * Show toast based on HTTP status
   * @param {number} status - HTTP status code
   * @param {string} message - Message to show
   * @param {Object} options - Additional options
   */
  const fromStatus = (status, message, options = {}) => {
    if (status >= 200 && status < 300) {
      return success(message, options)
    } else if (status >= 400 && status < 500) {
      return warning(message, options)
    } else if (status >= 500) {
      return error(message, options)
    } else {
      return info(message, options)
    }
  }

  /**
   * Batch operations
   */
  const batch = {
    success: (messages) => messages.forEach(msg => success(msg)),
    error: (messages) => messages.forEach(msg => error(msg)),
    warning: (messages) => messages.forEach(msg => warning(msg)),
    info: (messages) => messages.forEach(msg => info(msg))
  }

  return {
    // Basic methods
    success,
    error,
    warning,
    info,
    loading,

    // Management methods
    remove,
    clear,
    update,

    // Advanced methods
    promise,
    api,
    fromStatus,
    batch,

    // Direct access to store
    store: toastStore
  }
}
