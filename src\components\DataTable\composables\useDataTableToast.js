import { reactive } from 'vue'

// Global state for DataTable toast notifications
const toastState = reactive({
  toasts: []
})

let toastId = 0

export function useDataTableToast() {
  
  function addToast(message, type = 'info', duration = 3000) {
    const id = ++toastId
    const toast = {
      id,
      message,
      type, // success, error, warning, info
      duration,
      show: true
    }
    
    toastState.toasts.push(toast)
    
    // Auto remove after duration
    if (duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, duration)
    }
    
    return id
  }
  
  function removeToast(id) {
    const index = toastState.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      toastState.toasts.splice(index, 1)
    }
  }
  
  function clearAllToasts() {
    toastState.toasts = []
  }
  
  // Convenience methods
  function success(message, duration = 3000) {
    return addToast(message, 'success', duration)
  }
  
  function error(message, duration = 5000) {
    return addToast(message, 'error', duration)
  }
  
  function warning(message, duration = 4000) {
    return addToast(message, 'warning', duration)
  }
  
  function info(message, duration = 3000) {
    return addToast(message, 'info', duration)
  }
  
  return {
    // State
    toastState,
    
    // Methods
    addToast,
    removeToast,
    clearAllToasts,
    success,
    error,
    warning,
    info
  }
}

// Export the global state for use in components
export { toastState }
