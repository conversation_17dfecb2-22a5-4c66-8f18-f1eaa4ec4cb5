<script setup>
import { useConfirmation } from '@/composables/useConfirmation'
import { toast } from '@/plugins/toast'

const { showConfirmation, showDeleteConfirmation, showBulkDeleteConfirmation } = useConfirmation()

async function testDeleteConfirmation() {
  const confirmed = await showDeleteConfirmation()
  if (confirmed) {
    toast.success('User confirmed deletion!')
  } else {
    toast.info('User cancelled deletion')
  }
}

async function testBulkDeleteConfirmation() {
  const confirmed = await showBulkDeleteConfirmation(5)
  if (confirmed) {
    toast.success('User confirmed bulk deletion!')
  } else {
    toast.info('User cancelled bulk deletion')
  }
}

async function testCustomConfirmation() {
  const confirmed = await showConfirmation({
    title: 'Save Changes',
    message: 'Do you want to save your changes before leaving this page?',
    confirmText: 'Save & Continue',
    cancelText: 'Discard Changes',
    type: 'warning'
  })

  if (confirmed) {
    toast.success('User chose to save!')
  } else {
    toast.warning('User chose to discard')
  }
}

async function testSuccessConfirmation() {
  const confirmed = await showConfirmation({
    title: 'Operation Complete',
    message: 'Your data has been successfully processed. Would you like to view the results?',
    confirmText: 'View Results',
    cancelText: 'Close',
    type: 'success'
  })

  if (confirmed) {
    toast.success('User wants to view results!')
  }
}
</script>

<template>
  <div class="p-8 max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Confirmation Modal Examples</h1>

    <div class="space-y-4">
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Delete Confirmation</h2>
        <p class="text-gray-600 mb-4">Test the standard delete confirmation modal</p>
        <button @click="testDeleteConfirmation"
          class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
          Test Delete Confirmation
        </button>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Bulk Delete Confirmation</h2>
        <p class="text-gray-600 mb-4">Test the bulk delete confirmation modal</p>
        <button @click="testBulkDeleteConfirmation"
          class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
          Test Bulk Delete (5 items)
        </button>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Custom Warning Confirmation</h2>
        <p class="text-gray-600 mb-4">Test a custom warning confirmation modal</p>
        <button @click="testCustomConfirmation"
          class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors">
          Test Warning Confirmation
        </button>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Success Confirmation</h2>
        <p class="text-gray-600 mb-4">Test a success confirmation modal</p>
        <button @click="testSuccessConfirmation"
          class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
          Test Success Confirmation
        </button>
      </div>
    </div>

    <div class="mt-8 p-4 bg-blue-50 rounded-lg">
      <h3 class="font-semibold text-blue-900 mb-2">How it works:</h3>
      <ul class="text-blue-800 text-sm space-y-1">
        <li>• No more ugly browser alerts!</li>
        <li>• Beautiful, customizable modal dialogs</li>
        <li>• Different types: danger, warning, info, success</li>
        <li>• Loading states and smooth animations</li>
        <li>• Automatically integrated with DataTable delete operations</li>
      </ul>
    </div>
  </div>
</template>
