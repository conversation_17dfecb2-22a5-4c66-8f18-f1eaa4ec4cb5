<script setup>
import { computed } from 'vue'

import { IMAGE_URL } from '@/api/endpoint'

const emit = defineEmits(['close']);

// Import user props
const props = defineProps(['user']);

// Computed properties for cleaner code
const currentUserImage = computed(() => {
    if (props.user?.image_url) {
        return IMAGE_URL + props.user.image_url
    }
    return null
})

const userStatus = computed(() => {
    return props.user?.status === 1 ? 'Active' : 'Inactive'
})

const userStatusClass = computed(() => {
    return props.user?.status === 1
        ? 'bg-green-500/20 text-green-100 border-green-400/30'
        : 'bg-red-500/20 text-red-100 border-red-400/30'
})

const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}
</script>

<template>
    <transition name="fade">
        <div class="modal-overlay fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm bg-black/30"
            @click.self="$emit('close')">
            <div
                class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-4xl relative animate-fadeIn overflow-hidden flex">
                <!-- Left side - User Profile -->
                <div class="modal-side bg-blue-600 text-white w-1/3 p-8 flex flex-col items-center justify-center">
                    <!-- Large User Profile Image -->
                    <div class="flex flex-col items-center text-center">
                        <div
                            class="w-40 h-40 rounded-full overflow-hidden bg-white/20 flex items-center justify-center mb-6 border-4 border-white/30 shadow-xl">
                            <img v-if="currentUserImage" :src="currentUserImage" :alt="props.user?.name || 'User'"
                                class="w-full h-full object-cover" />
                            <svg v-else xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round"
                                stroke-linejoin="round" class="opacity-60">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </div>

                        <div class="text-center">
                            <h3 class="text-2xl font-bold mb-2">{{ props.user?.name || 'User Name' }}</h3>
                            <p class="text-blue-100 mb-1">{{ props.user?.user_position || 'Position' }}</p>
                            <p class="text-blue-200 text-sm">{{ props.user?.email || 'Email' }}</p>
                        </div>

                        <!-- Status Badge -->
                        <div class="mt-4 px-4 py-2 rounded-lg border" :class="userStatusClass">
                            <p class="text-xs font-medium uppercase tracking-wide">{{ userStatus }}</p>
                        </div>

                        <div class="mt-4 px-4 py-2 bg-white/10 rounded-lg backdrop-blur-sm">
                            <p class="text-xs text-blue-100 uppercase tracking-wide">User Profile</p>
                        </div>
                    </div>
                </div>

                <!-- Right side - User Details -->
                <div class="w-2/3">
                    <div class="modal-header border-b border-gray-100 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800">User Details</h2>
                        <button
                            class="close-btn text-gray-400 hover:text-gray-700 hover:bg-gray-100 rounded-full p-2 transition-colors"
                            @click="$emit('close')" aria-label="Close">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>

                    <div class="modal-body p-6 max-h-[calc(100vh-200px)] overflow-y-auto">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Personal Information -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Personal
                                    Information</h3>

                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ props.user?.name ||
                                            'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Email
                                            Address</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ props.user?.email ||
                                            'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Position</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{
                                            props.user?.user_position || 'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Status</label>
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                            :class="props.user?.status === 1
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'">
                                            <span class="w-2 h-2 rounded-full mr-2"
                                                :class="props.user?.status === 1 ? 'bg-green-400' : 'bg-red-400'"></span>
                                            {{ userStatus }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Account
                                    Information</h3>

                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">User ID</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">#{{ props.user?.id ||
                                            'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Email
                                            Verified</label>
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                            :class="props.user?.email_verified_at
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-yellow-100 text-yellow-800'">
                                            {{ props.user?.email_verified_at ? 'Verified' : 'Not Verified' }}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Created Date</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{
                                            formatDate(props.user?.created_at) }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{
                                            formatDate(props.user?.updated_at) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer flex justify-end gap-3 mt-6 pt-4 border-t border-gray-100">
                            <button type="button"
                                class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors font-medium"
                                @click="$emit('close')">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

@keyframes fadeIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

.modal-side {
    background-image: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
    position: relative;
    overflow: hidden;
}

.modal-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.modal-content {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    height: auto;
    max-height: 90vh;
}

.close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .modal-content {
        flex-direction: column;
        max-width: 95%;
        max-height: 95vh;
    }

    .modal-side {
        width: 100%;
        height: auto;
        min-height: 200px;
        padding: 20px;
        flex-direction: column;
        justify-content: center;
    }

    .modal-side .w-40 {
        width: 120px;
        height: 120px;
    }

    .modal-side h3 {
        font-size: 1.5rem;
    }

    .w-2\/3 {
        width: 100%;
    }

    .grid-cols-1.md\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}
</style>